import React from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import { Text, Surface } from "react-native-paper";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AppStackParamList } from "../../types";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { Colors, Spacing, Typography } from "../../theme";

const PrivacyPolicyScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AppStackParamList>>();
  const { t } = useLanguage();
  const { isDarkMode, colors } = useTheme();

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <MaterialCommunityIcons
          name="chevron-left"
          size={28}
          color={Colors.WHITE}
        />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{t("privacyPolicy.title")}</Text>
    </View>
  );

  const renderSection = (title: string, content: string) => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
        {title}
      </Text>
      <Text style={[styles.sectionContent, { color: colors.TEXT_SECONDARY }]}>
        {content}
      </Text>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND }]}>
      <StatusBar
        backgroundColor={Colors.PRIMARY}
        barStyle={isDarkMode ? "light-content" : "dark-content"}
      />
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Surface
          style={[
            styles.card,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={2}
        >
          <Text style={[styles.lastUpdated, { color: colors.TEXT_SECONDARY }]}>
            {t("privacyPolicy.lastUpdated")}
          </Text>

          {renderSection(
            t("privacyPolicy.introduction"),
            t("privacyPolicy.introText")
          )}

          {renderSection(
            t("privacyPolicy.informationCollection"),
            ""
          )}

          <View style={styles.subsection}>
            <Text style={[styles.subsectionTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("privacyPolicy.personalInfo")}
            </Text>
            <Text style={[styles.sectionContent, { color: colors.TEXT_SECONDARY }]}>
              {t("privacyPolicy.personalInfoText")}
            </Text>
          </View>

          <View style={styles.subsection}>
            <Text style={[styles.subsectionTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("privacyPolicy.usageData")}
            </Text>
            <Text style={[styles.sectionContent, { color: colors.TEXT_SECONDARY }]}>
              {t("privacyPolicy.usageDataText")}
            </Text>
          </View>

          {renderSection(
            t("privacyPolicy.howWeUse"),
            t("privacyPolicy.useText")
          )}

          {renderSection(
            t("privacyPolicy.dataSharing"),
            t("privacyPolicy.sharingText")
          )}

          {renderSection(
            t("privacyPolicy.dataSecurity"),
            t("privacyPolicy.securityText")
          )}

          {renderSection(
            t("privacyPolicy.yourRights"),
            t("privacyPolicy.rightsText")
          )}

          {renderSection(
            t("privacyPolicy.contact"),
            t("privacyPolicy.contactText")
          )}
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 50,
    paddingBottom: Spacing.SPACING.lg,
    paddingHorizontal: Spacing.SPACING.lg,
    flexDirection: "row",
    alignItems: "center",
    ...Spacing.SHADOW.md,
  },
  backButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: Spacing.BORDER_RADIUS.round,
    padding: Spacing.SPACING.xs,
    marginRight: Spacing.SPACING.md,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    letterSpacing: 0.5,
  },
  content: {
    flex: 1,
    padding: Spacing.SPACING.lg,
  },
  card: {
    borderRadius: Spacing.BORDER_RADIUS.lg,
    padding: Spacing.SPACING.xl,
    marginBottom: Spacing.SPACING.lg,
  },
  lastUpdated: {
    fontSize: Typography.FONT_SIZE.sm,
    fontStyle: "italic",
    marginBottom: Spacing.SPACING.xl,
    textAlign: "center",
  },
  section: {
    marginBottom: Spacing.SPACING.xl,
  },
  sectionTitle: {
    fontSize: Typography.FONT_SIZE.lg,
    fontWeight: Typography.FONT_WEIGHT.bold,
    marginBottom: Spacing.SPACING.md,
    letterSpacing: 0.3,
  },
  sectionContent: {
    fontSize: Typography.FONT_SIZE.md,
    lineHeight: 24,
    textAlign: "justify",
  },
  subsection: {
    marginBottom: Spacing.SPACING.lg,
    marginLeft: Spacing.SPACING.md,
  },
  subsectionTitle: {
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.semibold,
    marginBottom: Spacing.SPACING.sm,
  },
});

export default PrivacyPolicyScreen;
