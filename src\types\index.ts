export type RootStackParamList = {
  Auth: undefined;
  App: undefined;
  RoomDetails: {
    roomId: string;
    roomNumber: string;
    motelId: string;
    initialStatus: "available" | "occupied" | "maintenance";
    price: number;
    deposit: number;
    area: number;
    amenities: string[];
    roomTypes: string[];
    note: string;
  };
  AddRoom: {
    defaultElectricityPrice?: string;
    defaultWaterPrice?: string;
    mode?: "add" | "edit";
    roomData?: any;
  };
  MotelsManagement: undefined;
  AddEditMotel: {
    motelId?: string; // undefined for create, string for edit
    mode: "create" | "edit";
  };
  UpdatePersonalInfo: undefined;
  BillScreen: {
    billId?: string;
    roomId: string;
    isEdit?: boolean;
  };
  EditHistoryScreen: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  SignUp: undefined;
  ForgotPassword: undefined;
  OTPVerification: {
    email: string;
    flow: "signup" | "forgotPassword";
    registrationData?: {
      name: string;
      email: string;
      password: string;
      verificationCode: string;
    };
  };
};

export type AppStackParamList = {
  MainTabs: undefined;
  AddRoom: {
    defaultElectricityPrice?: string;
    defaultWaterPrice?: string;
    mode?: "add" | "edit";
    roomData?: any;
  };
  RoomDetails: {
    roomId?: string;
    roomNumber?: string;
    motelId?: string;
    initialStatus?: "available" | "occupied" | "maintenance";
    price?: number;
    deposit?: number;
    area?: number;
    amenities?: string[];
    roomTypes?: string[];
    note?: string;
  };
  MotelsManagement: undefined;
  AddEditMotel: {
    motelId?: string; // undefined for create, string for edit
    mode: "create" | "edit";
  };
  UpdatePersonalInfo: undefined;
  BillScreen: {
    billId?: string;
    roomId: string;
    isEdit?: boolean;
  };
  EditHistoryScreen: undefined;
  PrivacyPolicy: undefined;
  TermsOfUse: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Report: undefined;
  Profile: undefined;
};

// Export all models
export * from "./models";
export * from "./auth";
